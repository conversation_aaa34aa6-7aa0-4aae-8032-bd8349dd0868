import discord
import logging
import re
import asyncio
import traceback
from typing import Optional, Dict, Any, List

from themethodbot.common.check_group_order import process_group_order

logger = logging.getLogger('themethodbot.latestsummary')

def format_cart_items(cart_items):
    """Format cart items for display in embeds."""
    formatted_items = []
    for item in cart_items:
        name = item.get('name', 'Unknown Item')
        quantity = item.get('quantity', 1)
        price = item.get('price', 0.0)

        if quantity > 1:
            formatted_items.append(f"{name} (x{quantity}) - ${price:.2f}")
        else:
            formatted_items.append(f"{name} - ${price:.2f}")

    return formatted_items

async def latestsummary(
    interaction: discord.Interaction,
    subtotal: float,
    fees: float,
    grouporderlink: str = None
):
    """Calculate and display an order summary with manual subtotal and fees."""
    try:
        logger.info(f"🚀 LATESTSUMMARY COMMAND START")
        logger.info(f"👤 Triggered by: {interaction.user} (ID: {interaction.user.id})")
        logger.info(f"💲 Input - Subtotal: ${subtotal:.2f}, Fees: ${fees:.2f}")
        logger.info(f"🔗 Group Order Link: {grouporderlink}")

        await interaction.response.defer()
        logger.info("⏳ Interaction deferred")

        # Initialize variables
        location = "Location not provided"
        cart_items = []

        if grouporderlink:
            logger.info(f"🔄 Processing group order link...")
            result = await process_group_order(grouporderlink)

            if result:
                # Ensure the group link is included in the result
                result['group_link'] = grouporderlink
                logger.info("✅ Group order processed successfully")

                loc = result.get('location', {})
                if loc:
                    location = f"{loc.get('address', 'Unknown')}, {loc.get('city', 'Unknown')}, {loc.get('state', 'XX').upper()}"
                    logger.info(f"📍 Location extracted: {location}")

                # Format cart items from dictionaries to strings
                raw_cart_items = result.get('cart_items', [])
                if raw_cart_items:
                    cart_items = format_cart_items(raw_cart_items)
                    logger.info(f"🛒 Retrieved {len(cart_items)} cart items")
                else:
                    logger.warning("⚠️ No cart items in result")

        # If no group order link provided, search in chat history
        if not grouporderlink:
            logger.info("🔍 No direct group order link - Searching channel history...")
            message_count = 0
            found_link = False

            async for message in interaction.channel.history(limit=50):
                message_count += 1
                logger.info(f"📝 Checking message {message_count}")

                pattern = r"https://(?:eats\.uber\.com/(?:[a-z]{2}(?:-[a-z]{2})?/)?group-orders/|www\.ubereats\.com/(?:[a-z]{2}(?:-[a-z]{2})?/)?group-orders/)[a-zA-Z0-9-]+(?:/join)?(?:\S*)"

                match = re.search(pattern, message.content)
                if match:
                    grouporderlink = match.group(0)
                    logger.info(f"✅ Found group order link: {grouporderlink}")
                    found_link = True
                    break

                for embed in message.embeds:
                    if embed.description:
                        match = re.search(pattern, embed.description)
                        if match:
                            grouporderlink = match.group(0)
                            logger.info(f"✅ Found group order link in embed: {grouporderlink}")
                            found_link = True
                            break

                if found_link:
                    break

            # Process group order if we found a link
            if grouporderlink:
                logger.info(f"🔄 Processing found group order link...")
                result = await process_group_order(grouporderlink)

                if result:
                    logger.info("✅ Group order processed successfully")

                    loc = result.get('location', {})
                    if loc:
                        location = f"{loc.get('address', 'Unknown')}, {loc.get('city', 'Unknown')}, {loc.get('state', 'XX').upper()}"
                        logger.info(f"📍 Location extracted: {location}")

                    # Format cart items from dictionaries to strings
                    raw_cart_items = result.get('cart_items', [])
                    if raw_cart_items:
                        cart_items = format_cart_items(raw_cart_items)
                        logger.info(f"🛒 Retrieved {len(cart_items)} cart items")
                    else:
                        logger.warning("⚠️ No cart items in result")

        # For latestsummary, we'll use the exact fees input by the user
        # NEW PRICING MODEL: $25 discount with overflow fee and $10 service fee

        # Calculate discount amount (fixed $25)
        discount_amount = 25.00

        # Calculate discounted subtotal (subtract $25)
        discounted_subtotal = round(subtotal - discount_amount, 2)

        # Calculate overflow fee (when discounted subtotal is positive)
        overflow_fee = round(max(0, discounted_subtotal), 2)

        # Set discounted subtotal to 0 if it was negative
        if discounted_subtotal < 0:
            discounted_subtotal = 0

        # Calculate savings (always $25 unless subtotal is less than $25)
        savings = round(min(subtotal, discount_amount), 2)

        # Set Method fee (increased from $9 to $10)
        method_fee = 10.00

        # Estimate taxes as a percentage of the subtotal (typically around 8%)
        estimated_taxes = round(subtotal * 0.08, 2)

        # Total fees = taxes + method fee + overflow fee
        total_fees = round(estimated_taxes + method_fee + overflow_fee, 2)

        # Create fee calculations dictionary with the new pricing model
        fee_calculations = {
            'subtotal': subtotal,
            'discounted_subtotal': discounted_subtotal,
            'discount_amount': discount_amount,
            'savings': savings,
            'overflow_fee': overflow_fee,
            'service_fee': 0,   # Not used in the new model
            'taxes': estimated_taxes,  # Estimated taxes
            'ca_driver_benefit': 0,
            'uber_one_discount': 0,
            'total_fees': total_fees,  # Taxes + method fee + overflow fee
            'final_fees': total_fees,  # Taxes + method fee + overflow fee
            'final_total': round(discounted_subtotal + estimated_taxes + method_fee + overflow_fee, 2),  # Discounted subtotal + taxes + method fee + overflow fee
            'is_cad': False,
            'method_fee': method_fee      # Add method fee for display
        }

        # Create result dictionary for the embed
        result = {
            'group_link': grouporderlink if grouporderlink else "Not available",
            'location': {'address': location},
            'store_url': ""    # No store URL available
        }

        # Import the embed template
        from themethodbot.embed_templates import create_latestsummary_embed

        # Create and send the embed
        embed = create_latestsummary_embed(result, cart_items, fee_calculations)

        # Import the payment methods selector
        from themethodbot.themethodbot import PaymentMethodSelector

        # Send the embed with payment methods buttons
        await interaction.followup.send(embed=embed, view=PaymentMethodSelector())

        logger.info("✅ Summary sent successfully")

    except Exception as e:
        logger.error(f"❌ Error in latestsummary command: {str(e)}")
        logger.error(traceback.format_exc())

        if not interaction.response.is_done():
            await interaction.response.send_message(
                f"❌ An error occurred: {str(e)}",
                ephemeral=True
            )
        else:
            await interaction.followup.send(
                f"❌ An error occurred: {str(e)}",
                ephemeral=True
            )
