import discord
import logging
import datetime
from typing import List, Dict, Any, Optional

logger = logging.getLogger('eatscheckerbot.embeds')

def create_locked_order_embed():
    """Create a modern embed for locked/canceled orders."""
    embed = discord.Embed(
        title="🔒 Group Order is Locked",
        description="This group order appears to be locked or canceled.",
        color=discord.Color.from_rgb(237, 66, 69)  # Discord Red
    )

    # Add spacing to the description
    embed.description += "\n\n"

    # Add steps with better formatting
    embed.add_field(
        name="🔸 Step 1",
        value="Go back to your Uber Eats Cart",
        inline=False
    )

    embed.add_field(
        name="🔹 Step 2",
        value="Press **Unlock Group Order**",
        inline=False
    )

    embed.add_field(
        name="🔸 Step 3",
        value="Resend your cart link to this channel",
        inline=False
    )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360138365621506048/padlock.gif?ex=67fa0710&is=67f8b590&hm=55b33ef59bdd15b35e8f627407e05a63340cae2955c2772389a47f5ea48c9be6&=")  # Lock icon

    return embed

def create_non_promo_embed():
    """Create a modern embed for stores not in promo."""
    embed = discord.Embed(
        title="🔍 Find Eligible Stores",
        description="**The store you're looking for isn't in our promo. Follow these steps to find eligible restaurants:**",
        color=discord.Color.from_rgb(254, 231, 92)  # Bright Yellow
    )

    # Add a thumbnail for a modern look
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360135017019347135/food.gif?ex=67fa03f2&is=67f8b272&hm=2722a45f152c6b1f0f7f52e2cf1e890bca3d70c6934d6349646ae7e43f76b375&=")  # Search/store icon

    # Add spacing to the description
    embed.description += "\n\n"

    # Step 1
    embed.add_field(
        name="🔸 Step 1: Open the Promo Link",
        value="Open [**this link**](https://tinyurl.com/TheMethodUE) in Chrome or Safari",
        inline=False
    )

    # Step 2
    embed.add_field(
        name="🔹 Step 2: Enter Your Address",
        value="Log in to Uber Eats and enter your delivery address",
        inline=False
    )

    # Step 3
    embed.add_field(
        name="🔸 Step 3: Open the Promo Link Again in a NEW tab",
        value="Open [**the same link**](https://tinyurl.com/TheMethodUE) in a second tab",
        inline=False
    )

    # Step 4
    embed.add_field(
        name="🔹 Step 4: It should say \"$25 off\", now you can Browse Eligible Restaurants",
        value="You'll now see all restaurants eligible for our promo!",
        inline=False
    )

    # Add a footer with a tip
    embed.set_footer(text="It should look like the image below")

    # Add the screenshot as a smaller image at the bottom
    embed.set_image(url="https://media.discordapp.net/attachments/978162420423999498/1349998616110043237/image.png?width=600&height=338")

    return embed

def create_order_summary_embed(result: Dict[str, Any], cart_items: List[str], fee_calculations: Dict[str, Any]) -> discord.Embed:
    """Create a modern order summary embed with enhanced visuals."""
    embed = discord.Embed(
        title="🍔 EATS Order Summary",
        color=discord.Color.from_rgb(87, 242, 135)  # Vibrant Green
    )

    # Add group order link if available with better formatting
    group_link = result.get('group_link', 'Not available')

    # Check if the store is in promo
    is_promo = result.get('is_promo', True)
    promo_status = "✅ Store is in promo!" if is_promo else "❌ Store is not in promo"

    embed.description = f"**🏷️ Promo Status:** {promo_status}\n**🔗 [Group Order Link]({group_link})**\n\n"

    # Add spacing to the description
    embed.description += "\n\n"

    # Location with better formatting
    location = result.get('location', {})
    location_str = f"{location.get('address', 'Unknown Address')}"
    if location.get('city'):
        location_str += f", {location.get('city')}"
    if location.get('state'):
        location_str += f", {location.get('state').upper()}"
    if location.get('zipcode'):
        location_str += f" {location.get('zipcode')}"

    embed.add_field(
        name="📍 Delivery Location",
        value=location_str,
        inline=False
    )

    # Add store URL if available
    store_url = result.get('store_url', '')
    if store_url:
        embed.add_field(
            name="🏪 Restaurant",
            value=f"[View on Uber Eats]({store_url})",
            inline=False
        )

    # Add cart items with better formatting
    if cart_items:
        # Limit to 10 items to avoid embed being too large
        items_to_show = cart_items[:10]
        remaining_items = len(cart_items) - 10 if len(cart_items) > 10 else 0

        items_str = "\n".join([f"• {item}" for item in items_to_show])
        if remaining_items > 0:
            items_str += f"\n• ... and {remaining_items} more items"

        embed.add_field(
            name=f"🛒 Order Items ({len(cart_items)})",
            value=items_str,
            inline=False
        )

    # Price breakdown with better formatting
    currency = "CA$" if fee_calculations.get('is_cad', False) else "$"

    # Original vs. discounted price
    embed.add_field(
        name="💰 Price Breakdown",
        value=f"Original Subtotal: `{currency}{fee_calculations.get('subtotal', 0):.2f}`\n" +
              f"Discounted (70% OFF): `{currency}{fee_calculations.get('discounted_subtotal', 0):.2f}`\n" +
              f"You Save: `{currency}{fee_calculations.get('savings', 0):.2f}`",
        inline=True
    )

    # Fees breakdown
    fees_str = ""

    if fee_calculations.get('overflow_fee', 0) > 0:
        fees_str += f"Overflow Fee: `{currency}{fee_calculations.get('overflow_fee', 0):.2f}`\n"

    fees_str += f"Service Fee: `{currency}{fee_calculations.get('service_fee', 0):.2f}`\n"

    if fee_calculations.get('ca_driver_benefit', 0) > 0:
        fees_str += f"Driver Benefit: `{currency}{fee_calculations.get('ca_driver_benefit', 0):.2f}`\n"

    fees_str += f"Taxes: `{currency}{fee_calculations.get('taxes', 0):.2f}`"

    # Calculate and add Uber One discount (11% of subtotal)
    subtotal = fee_calculations.get('subtotal', 0)
    uber_one_discount = round(subtotal * 0.11, 2)
    fees_str += f"\nUber One Discount: `-{currency}{uber_one_discount:.2f}`"

    # Calculate and add final fees total
    service_fee = fee_calculations.get('service_fee', 0)
    ca_driver_benefit = fee_calculations.get('ca_driver_benefit', 0)
    taxes = fee_calculations.get('taxes', 0)
    total_fees_before_discount = service_fee + ca_driver_benefit + taxes
    final_fees = round(total_fees_before_discount - uber_one_discount, 2)

    # Check if service fee and taxes are both $0, which indicates "You pay for your portion" setting
    # This check is more reliable than checking final_fees which can be negative due to Uber One discount
    if service_fee == 0 and taxes == 0 and ca_driver_benefit == 0:
        fees_str = "**Unable to fetch pricing**\nGroup order is set to \"You pay for your portion\""
    else:
        fees_str += f"\n\n**Total Fees:** `{currency}{final_fees:.2f}`"

    embed.add_field(
        name="💸 Fees & Taxes",
        value=fees_str,
        inline=False
    )

    # Calculate and display final total with emphasis
    discounted_subtotal = fee_calculations.get('discounted_subtotal', 0)
    overflow_fee = fee_calculations.get('overflow_fee', 0)
    final_total = round(discounted_subtotal + overflow_fee + final_fees, 2)

    # Calculate the before amount (original subtotal + all fees before discount)
    original_subtotal = fee_calculations.get('subtotal', 0)
    service_fee = fee_calculations.get('service_fee', 0)
    ca_driver_benefit = fee_calculations.get('ca_driver_benefit', 0)
    taxes = fee_calculations.get('taxes', 0)
    overflow_fee = fee_calculations.get('overflow_fee', 0)

    # Sum all components for the before amount
    before_amount = round(original_subtotal + service_fee + ca_driver_benefit + taxes + overflow_fee, 2)

    # Only show final total if it's not zero and we have valid fee data
    if final_total > 0 and not (service_fee == 0 and taxes == 0 and ca_driver_benefit == 0):
        embed.add_field(
            name="💵 Final Total (Tip not included)",
            value=f"`{currency}{before_amount:.2f}` ➡️ **`{currency}{final_total:.2f}`**",
            inline=False
        )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360135017019347135/food.gif?ex=67fa03f2&is=67f8b272&hm=2722a45f152c6b1f0f7f52e2cf1e890bca3d70c6934d6349646ae7e43f76b375&=")  # Food/order icon

    # Add footer with helpful information - only show estimate warning if we have valid pricing
    if service_fee == 0 and taxes == 0 and ca_driver_benefit == 0:
        embed.set_footer(text="EATS | Order Summary")
    else:
        embed.set_footer(text="⚠️ This is an ESTIMATE. Please wait for a Chef to confirm your final prices | EATS")

    return embed

def check_order_limits(subtotal: float, is_cad: bool) -> Optional[discord.Embed]:
    """Check if order meets minimum/maximum requirements with modern embed."""
    currency = "CA$" if is_cad else "$"
    min_order = 30.0 if is_cad else 24.0
    max_order = 35.0 if is_cad else 30.0

    if subtotal < min_order:
        embed = discord.Embed(
            title="⚠️ Minimum Order Requirement",
            description=f"Your subtotal must be at least `{currency}{min_order:.2f}` before fees and taxes.",
            color=discord.Color.from_rgb(254, 231, 92)  # Bright Yellow
        )

        # Add spacing to the description
        embed.description += "\n\n"

        # Add action required
        embed.add_field(
            name="👉 Action Required",
            value="Please add more items to your cart to meet the minimum requirement.",
            inline=False
        )

        # Add current vs required
        embed.add_field(
            name="📊 Current vs Required",
            value=f"**Current Subtotal:** `{currency}{subtotal:.2f}`\n" +
                  f"**Minimum Required:** `{currency}{min_order:.2f}`\n" +
                  f"**Amount to Add:** `{currency}{(min_order - subtotal):.2f}`",
            inline=False
        )

        # Set a nice thumbnail
        embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360139062299852952/alert.gif?ex=67fa07b6&is=67f8b636&hm=09a26cf7707c99cc864ca3e62a8726ea0c08f2b3b2a74917489b35c2cb1d8471&=")  # Warning icon

        return embed

    elif subtotal > max_order:
        embed = discord.Embed(
            title="⚠️ Maximum Order Limit",
            description=f"Your subtotal must be at most `{currency}{max_order:.2f}` before fees and taxes.",
            color=discord.Color.from_rgb(254, 231, 92)  # Bright Yellow
        )

        # Add spacing to the description
        embed.description += "\n\n"

        # Add action required
        embed.add_field(
            name="👉 Action Required",
            value="Please remove some items from your cart to meet the maximum limit.",
            inline=False
        )

        # Add current vs required
        embed.add_field(
            name="📊 Current vs Limit",
            value=f"**Current Subtotal:** `{currency}{subtotal:.2f}`\n" +
                  f"**Maximum Allowed:** `{currency}{max_order:.2f}`\n" +
                  f"**Amount to Remove:** `{currency}{(subtotal - max_order):.2f}`",
            inline=False
        )

        # Set a nice thumbnail
        embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360139062299852952/alert.gif?ex=67fa07b6&is=67f8b636&hm=09a26cf7707c99cc864ca3e62a8726ea0c08f2b3b2a74917489b35c2cb1d8471&=")  # Warning icon

        return embed

    return None

def create_error_embed(error_message: str) -> discord.Embed:
    """Create a modern error embed."""
    embed = discord.Embed(
        title="❌ Error Processing Order",
        description=f"We encountered an error while processing your order:\n\n```{error_message}```",
        color=discord.Color.from_rgb(237, 66, 69)  # Discord Red
    )

    # Add spacing to the description
    embed.description += "\n\n"

    # Add troubleshooting steps
    embed.add_field(
        name="🔍 Troubleshooting Steps",
        value="1. Make sure your group order is unlocked\n" +
              "2. Try creating a new group order\n" +
              "3. Check that you're using a supported restaurant",
        inline=False
    )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360139294890655936/alert_1.gif?ex=67fa07ee&is=67f8b66e&hm=3a983f0f8ae1980bc4abe3fc77efa37b61c8fb862570944ce1ca4290ab3e4b78&=")  # Warning icon

    # Add footer with support info
    embed.set_footer(text="If the issue persists, please contact support")

    return embed

def create_processing_embed() -> discord.Embed:
    """Create a modern processing embed."""
    embed = discord.Embed(
        title="🔄 Processing Your Order",
        description="We're analyzing your Uber Eats group order. This may take a few seconds...",
        color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple
    )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360139708234993674/loading.gif?ex=67fa0850&is=67f8b6d0&hm=a4286ff0bd61b871ec51cb03ce8a589cd24d93f101bb10fa414567816d9c4a57&=")  # Loading animation

    return embed

def create_latestsummary_embed(result: Dict[str, Any], cart_items: List[str], fee_calculations: Dict[str, Any]) -> discord.Embed:
    """Create a special embed specifically for the /latestsummary command."""
    embed = discord.Embed(
        title="🍔 EATS Order Summary",
        color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple - different color from regular summary
    )

    # Add group order link if available
    group_link = result.get('group_link', 'Not available')
    if group_link != 'Not available':
        embed.description = f"🔗 **[Group Order Link]({group_link})**\n\n"
    else:
        embed.description = ""

    # Add cart items with better formatting
    if cart_items:
        # Limit to 10 items to avoid embed being too large
        items_to_show = cart_items[:10]
        remaining_items = len(cart_items) - 10 if len(cart_items) > 10 else 0

        items_str = ""
        for item in items_to_show:
            items_str += f"╰・ *{item}*\n"

        if remaining_items > 0:
            items_str += f"╰・ *... and {remaining_items} more items*"

        embed.add_field(
            name=f"🛒 Order Items ({len(cart_items)})",
            value=items_str,
            inline=False
        )

    # Price breakdown with better formatting
    currency = "CA$" if fee_calculations.get('is_cad', False) else "$"

    # Original vs. discounted price
    embed.add_field(
        name="💰 Price Breakdown",
        value=f"Original Subtotal: `{currency}{fee_calculations.get('subtotal', 0):.2f}`\n" +
              f"Discounted (70% OFF): `{currency}{fee_calculations.get('discounted_subtotal', 0):.2f}`\n" +
              f"You Save: `{currency}{fee_calculations.get('savings', 0):.2f}`",
        inline=True
    )

    # Simplified fees
    total_fees = fee_calculations.get('final_fees', 0)

    # Get individual fee components for a more reliable check
    service_fee = fee_calculations.get('service_fee', 0)
    ca_driver_benefit = fee_calculations.get('ca_driver_benefit', 0)
    taxes = fee_calculations.get('taxes', 0)

    # Check if service fee and taxes are both $0, which indicates "You pay for your portion" setting
    # This check is more reliable than checking final_fees which can be negative due to Uber One discount
    if service_fee == 0 and taxes == 0 and ca_driver_benefit == 0:
        embed.add_field(
            name="💸 Fees:",
            value="**Unable to fetch pricing**\nGroup order is set to \"You pay for your portion\"",
            inline=False
        )
    else:
        embed.add_field(
            name="<:flyingmoney:1360153492324487169> Fees:",
            value=f"`{currency}{total_fees:.2f}`",
            inline=False
        )

    # Use the pre-calculated final_total from fee_calculations
    # This ensures we use the exact value calculated in latestsummary.py
    final_total = fee_calculations.get('final_total', 0)

    # Calculate the before amount (original subtotal + fees)
    original_subtotal = fee_calculations.get('subtotal', 0)
    before_amount = round(original_subtotal + total_fees, 2)

    # Only show final total if it's not zero and we have valid fee data
    if final_total > 0 and not (service_fee == 0 and taxes == 0 and ca_driver_benefit == 0):
        embed.add_field(
            name="💵 Final Total (Tip not included)",
            value=f"`{currency}{before_amount:.2f}` ➡️ **`{currency}{final_total:.2f}`**",
            inline=False
        )

    # Set a different thumbnail for latestsummary
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360135017019347135/food.gif?ex=67fa03f2&is=67f8b272&hm=2722a45f152c6b1f0f7f52e2cf1e890bca3d70c6934d6349646ae7e43f76b375&=")

    # No warning message in the footer for latestsummary
    embed.set_footer(text="EATS | Order Summary")

    return embed

def create_open_status_embed(service_name: str, clocked_in_staff_names: list = None) -> discord.Embed:
    """Create a modern embed for open status."""
    embed = discord.Embed(
        title=f"🟢 {service_name} is Now Open!",
        description=f"**We are now accepting orders!** Place your order using the instructions below.",
        color=discord.Color.from_rgb(87, 242, 135)  # Vibrant Green
    )

    # Add clocked-in staff if provided
    if clocked_in_staff_names and len(clocked_in_staff_names) > 0:
        staff_list = "\n".join([f"• {name}" for name in clocked_in_staff_names])
        embed.add_field(
            name="👨‍🍳 Chefs Clocked In Doing Orders:",
            value=staff_list,
            inline=False
        )

    # Set image and footer


    return embed

def create_close_status_embed(service_name: str) -> discord.Embed:
    """Create a modern embed for closed status."""
    embed = discord.Embed(
        title=f"🔴 {service_name} is Now Closed!",
        description="**We are currently closed.** Please check back later for updates.",
        color=discord.Color.from_rgb(237, 66, 69)  # Discord Red
    )

    # Add some spacing between title and content
    embed.description += "\n"

    # Set image and footer
    embed.set_footer(text=f"{service_name} | Currently Closed")

    return embed

def create_clock_in_embed(user: discord.Member, clocked_in_by: discord.Member = None) -> discord.Embed:
    """Create a modern embed for clock in notifications."""
    if clocked_in_by and clocked_in_by.id != user.id:
        title = f"🕒 {user.display_name} has been clocked in!"
        description = f"**{user.mention}** has been clocked in by **{clocked_in_by.mention}**"
    else:
        title = f"🕒 {user.display_name} has clocked in!"
        description = f"**{user.mention}** is now available to help with orders."

    embed = discord.Embed(
        title=title,
        description=description,
        color=discord.Color.from_rgb(87, 242, 135)  # Vibrant Green
    )

    # Add timestamp
    embed.timestamp = datetime.datetime.now()

    # Set user avatar as thumbnail if available
    if user.avatar:
        embed.set_thumbnail(url=user.avatar.url)

    # Add footer
    embed.set_footer(text="Staff Management System")

    return embed

def create_clock_out_embed(user: discord.Member) -> discord.Embed:
    """Create a modern embed for clock out notifications."""
    embed = discord.Embed(
        title=f"🕒 {user.display_name} has clocked out!",
        description=f"**{user.mention}** is no longer on duty.",
        color=discord.Color.from_rgb(237, 66, 69)  # Discord Red
    )

    # Add timestamp
    embed.timestamp = datetime.datetime.now()

    # Set user avatar as thumbnail if available
    if user.avatar:
        embed.set_thumbnail(url=user.avatar.url)

    # Add footer
    embed.set_footer(text="Staff Management System")

    return embed
